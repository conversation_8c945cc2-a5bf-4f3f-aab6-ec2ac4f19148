import { IsString, <PERSON>Number, IsEnum, IsOptional, IsA<PERSON>y, <PERSON>, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ReportStatus } from '../enums/report-status.enum';

export class CreateCommissionReportDto {
  @IsString()
  employerEmail: string;

  @IsNumber()
  @Min(1)
  @Max(12)
  month: number;

  @IsNumber()
  @Min(2020)
  year: number;

  @IsNumber()
  totalAmount: number;

  @IsOptional()
  @IsEnum(ReportStatus)
  status?: ReportStatus;

  @IsArray()
  @IsNumber({}, { each: true })
  commissionIds: number[];

  @IsOptional()
  @IsString()
  notes?: string;
}
