import { PartialType } from '@nestjs/mapped-types';
import { IsOptional, IsEnum, IsString } from 'class-validator';
import { CreateCommissionReportDto } from './create-commission-report.dto';
import { ReportStatus } from '../enums/report-status.enum';

export class UpdateCommissionReportDto extends PartialType(CreateCommissionReportDto) {
  @IsOptional()
  @IsEnum(ReportStatus)
  status?: ReportStatus;

  @IsOptional()
  @IsString()
  notes?: string;
}
