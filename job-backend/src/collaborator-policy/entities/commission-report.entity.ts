import { Entity, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToMany } from 'typeorm';
import { BaseSoftDeleteEntity } from '../../common/entities/base.entity';
import { ReportStatus } from '../enums/report-status.enum';
import { PaymentStatus } from '../enums/payment-status.enum';
import { EmployerCommission } from './employer-commission.entity';

@Entity('commission_reports')
export class CommissionReport extends BaseSoftDeleteEntity {

  @OneToMany(() => EmployerCommission, (commission) => commission.report)
  commissions: EmployerCommission[];

  @Column()
  employerEmail: string;

  @Column()
  month: number;

  @Column()
  year: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  totalAmount: number;

  @Column({
    type: 'enum',
    enum: ReportStatus,
    default: ReportStatus.PENDING
  })
  status: ReportStatus;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.UNPAID
  })
  paymentStatus: PaymentStatus;

  @Column({ type: 'datetime', nullable: true })
  paidDate: Date;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;
}
