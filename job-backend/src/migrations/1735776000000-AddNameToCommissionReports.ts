import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddNameToCommissionReports1735776000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'commission_reports',
      new TableColumn({
        name: 'name',
        type: 'varchar',
        length: '255',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('commission_reports', 'name');
  }
}
