import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateCommissionReportsTable1704067200000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'commission_reports',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'employerEmail',
            type: 'varchar',
            length: '255',
          },
          {
            name: 'month',
            type: 'int',
          },
          {
            name: 'year',
            type: 'int',
          },
          {
            name: 'totalAmount',
            type: 'decimal',
            precision: 15,
            scale: 2,
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['Pending', 'Approved', 'Paid', 'Cancelled'],
            default: "'Pending'",
          },
          {
            name: 'commissionIds',
            type: 'text',
          },
          {
            name: 'notes',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'approvedBy',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'approvedAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'paidAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'deletedAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'isDeleted',
            type: 'boolean',
            default: false,
          },
          {
            name: 'createdBy',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'updatedBy',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
        ],
        indices: [
          {
            name: 'IDX_COMMISSION_REPORTS_EMPLOYER_EMAIL',
            columnNames: ['employerEmail'],
          },
          {
            name: 'IDX_COMMISSION_REPORTS_MONTH_YEAR',
            columnNames: ['month', 'year'],
          },
          {
            name: 'IDX_COMMISSION_REPORTS_STATUS',
            columnNames: ['status'],
          },
          {
            name: 'IDX_COMMISSION_REPORTS_SOFT_DELETE',
            columnNames: ['isDeleted'],
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('commission_reports');
  }
}
