import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, <PERSON> } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Autocomplete
} from '@mui/material';
import { ArrowLeft, Save } from 'lucide-react';
import {
  getEmployerCommissionById,
  updateEmployerCommission,
  createEmployerCommission
} from '../../services/employerCommissionService';
import { getJobApplicationsForCommission } from '../../services/jobApplicationService';
import { getPaymentRateByCriteria } from '../../services/paymentRateService';
import {
  COMMISSION_STATUS,
  COMMISSION_PHASE,
  EMPLOYMENT_TYPE,
  EMPLOYMENT_TYPE_LABELS,
  LEVEL,
  ENGLISH_SKILL,
  COMMISSION_STATUS_LABELS,
  COMMISSION_PHASE_LABELS,
  LEVEL_LABELS,
  ENGLISH_SKILL_LABELS
} from '../../constants/collaboratorPolicy';
import { formatDateForInput, formatDateForAPI } from '../../utils/dateUtils';
import { formatCurrency } from '../../utils/currencyUtils';

const EmployerCommissionEditPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isCreateMode = !id;

  const [loading, setLoading] = useState(!isCreateMode);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const [formData, setFormData] = useState({
    employerEmail: '',
    jobApplicationId: '',
    employmentType: '',
    candidateLevel: '',
    englishSkill: '',
    commissionPhase: '',
    amount: '',
    status: isCreateMode ? COMMISSION_STATUS.PENDING : '',
    paidDate: '',
    onboardingDate: '',
    officialDate: '',
    notes: ''
  });

  const [filteredApplications, setFilteredApplications] = useState([]);
  const [selectedApplication, setSelectedApplication] = useState(null);
  const [applicationsLoaded, setApplicationsLoaded] = useState(false);
  const [loadingApplications, setLoadingApplications] = useState(false);
  const [lastLoadedEmail, setLastLoadedEmail] = useState('');
  const [loadingAmount, setLoadingAmount] = useState(false);
  const [previousCriteria, setPreviousCriteria] = useState({
    employmentType: '',
    candidateLevel: '',
    englishSkill: '',
    commissionPhase: ''
  });

  const loadJobApplications = async (refPerson = '') => {
    const needsReload = !applicationsLoaded || lastLoadedEmail !== refPerson;

    if (!needsReload || loadingApplications) return;

    setLoadingApplications(true);
    try {
      const applications = await getJobApplicationsForCommission(refPerson);
      setFilteredApplications(applications);

      // Merge with existing selectedApplication if it exists
      if (selectedApplication && selectedApplication.id) {
        const existingApp = applications.find(app => app.id === selectedApplication.id);
        if (existingApp) {
          setSelectedApplication(existingApp);
        } else {
          setFilteredApplications([selectedApplication, ...applications]);
        }
      }

      setApplicationsLoaded(true);
      setLastLoadedEmail(refPerson);
    } catch (error) {
      console.error('Failed to load job applications:', error);
    } finally {
      setLoadingApplications(false);
    }
  };

  const loadPaymentRateAmount = async (employmentType, candidateLevel, englishSkill, commissionPhase) => {
    if (!employmentType || !commissionPhase) {
      return;
    }

    if (employmentType === EMPLOYMENT_TYPE.FULLTIME && (!candidateLevel || !englishSkill)) {
      return;
    }

    const currentCriteria = { employmentType, candidateLevel, englishSkill, commissionPhase };
    const criteriaChanged = Object.keys(currentCriteria).some(
      key => currentCriteria[key] !== previousCriteria[key]
    );

    if (!criteriaChanged) {
      return;
    }

    setLoadingAmount(true);
    try {
      const paymentRate = await getPaymentRateByCriteria(employmentType, candidateLevel, englishSkill, commissionPhase);

      if (paymentRate && paymentRate.amount) {
        handleInputChange('amount', paymentRate.amount.toString());
      } else {
        handleInputChange('amount', '');
      }

      setPreviousCriteria(currentCriteria);
    } catch (error) {
      console.error('Failed to load payment rate:', error)
    } finally {
      setLoadingAmount(false);
    }
  };

  const isCommissionApproved = () => {
    return formData.status === COMMISSION_STATUS.APPROVED;
  };

  const loadCommission = async () => {
    setLoading(true);
    try {
      const response = await getEmployerCommissionById(id);
      const loadedData = {
        employerEmail: response.employerEmail || '',
        jobApplicationId: response.jobApplicationId || '',
        employmentType: response.employmentType || '',
        candidateLevel: response.candidateLevel || '',
        englishSkill: response.englishSkill || '',
        commissionPhase: response.commissionPhase || '',
        amount: response.amount || '',
        status: response.status || '',
        paidDate: formatDateForInput(response.paidDate),
        onboardingDate: formatDateForInput(response.onboardingDate),
        officialDate: formatDateForInput(response.officialDate),
        notes: response.notes || ''
      };

      setFormData(loadedData);
      setPreviousCriteria({
        employmentType: loadedData.employmentType,
        candidateLevel: loadedData.candidateLevel,
        englishSkill: loadedData.englishSkill,
        commissionPhase: loadedData.commissionPhase
      });

      if (response.employerEmail && response.jobApplicationId) {
        try {
          const applications = await getJobApplicationsForCommission(response.employerEmail);
          setFilteredApplications(applications);
          
          const selectedApp = applications.find(app => app.id === response.jobApplicationId);
          if (selectedApp) {
            setSelectedApplication(selectedApp);
          }
          
          setApplicationsLoaded(true);
          setLastLoadedEmail(response.employerEmail);
        } catch (error) {
          console.error('Failed to load job applications:', error);
        }
      }

      setError(null);
    } catch (error) {
      console.error('Failed to load commission:', error);
      setError('Failed to load commission details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      loadCommission();
    }
  }, [id]);

  useEffect(() => {
    if (formData.employerEmail !== lastLoadedEmail && selectedApplication) {
      setSelectedApplication(null);
      handleInputChange('jobApplicationId', '');
    }
  }, [formData.employerEmail, lastLoadedEmail, selectedApplication]);

  useEffect(() => {
    loadPaymentRateAmount(
      formData.employmentType,
      formData.candidateLevel,
      formData.englishSkill,
      formData.commissionPhase
    );
  }, [formData.employmentType, formData.candidateLevel, formData.englishSkill, formData.commissionPhase]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleApplicationChange = (_, newValue) => {
    setSelectedApplication(newValue);
    if (newValue) {
      handleInputChange('jobApplicationId', newValue.id);
    } else {
      handleInputChange('jobApplicationId', '');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    if (!formData.amount) {
      setError('Amount is required. Please ensure all criteria are selected to calculate commission.');
      setSaving(false);
      return;
    }

    try {
      const submitData = {
        ...formData,
        amount: parseFloat(formData.amount),
        jobApplicationId: parseInt(formData.jobApplicationId),
        paidDate: formatDateForAPI(formData.paidDate),
        onboardingDate: formatDateForAPI(formData.onboardingDate),
        officialDate: formatDateForAPI(formData.officialDate),
        candidateLevel: formData.candidateLevel || undefined,
        englishSkill: formData.englishSkill || undefined,
      };

      if (isCreateMode) {
        const response = await createEmployerCommission(submitData);
        setSuccess(true);
        setTimeout(() => {
          navigate(`/employer-commissions/${response.id}`);
        }, 1500);
      } else {
        await updateEmployerCommission(id, submitData);
        setSuccess(true);
        setTimeout(() => {
          navigate(`/employer-commissions/${id}`);
        }, 1500);
      }
    } catch (error) {
      console.error(`Failed to ${isCreateMode ? 'create' : 'update'} commission:`, error);
      setError(`Failed to ${isCreateMode ? 'create' : 'update'} commission. Please try again.`);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', p: 3 }}>
            <CircularProgress />
          </Box>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" component="h1">
              {isCreateMode ? 'Create New Commission' : 'Edit Commission'}
            </Typography>
            <Button
              variant="outlined"
              startIcon={<ArrowLeft />}
              onClick={() => navigate(isCreateMode ? '/employer-commissions' : `/employer-commissions/${id}`)}
            >
              {isCreateMode ? 'Back to List' : 'Back to Details'}
            </Button>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Commission {isCreateMode ? 'created' : 'updated'} successfully! Redirecting...
            </Alert>
          )}

          <Paper sx={{ p: 3 }}>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    label="Employer Email"
                    value={formData.employerEmail}
                    onChange={(e) => handleInputChange('employerEmail', e.target.value)}
                    disabled={isCommissionApproved()}
                    fullWidth
                    required
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Autocomplete
                    options={filteredApplications}
                    getOptionLabel={(option) => option.name}
                    value={selectedApplication}
                    onChange={handleApplicationChange}
                    onOpen={() => loadJobApplications(formData.employerEmail)}
                    readOnly={isCommissionApproved()}
                    loading={loadingApplications}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Job Application"
                        required
                        helperText="Enter Employer Email first to filter by Ref Person"
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {loadingApplications ? <CircularProgress color="inherit" size={20} /> : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        }}
                      />
                    )}
                    renderOption={(props, option) => (
                      <Box component="li" {...props}>
                        <Box>
                          <Typography variant="body1">
                            <Link
                              to={`/applicants/${option.id}`}
                              style={{ textDecoration: 'none', color: 'inherit' }}
                            >
                              {option.name}
                            </Link>
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {option.jobTitle} - {option.email}
                          </Typography>
                        </Box>
                      </Box>
                    )}
                    fullWidth
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth required>
                    <InputLabel>Employment Type</InputLabel>
                    <Select
                      value={formData.employmentType}
                      label="Employment Type"
                      onChange={(e) => handleInputChange('employmentType', e.target.value)}
                      readOnly={isCommissionApproved()}
                    >
                      {Object.values(EMPLOYMENT_TYPE).map((type) => (
                        <MenuItem key={type} value={type}>
                          {EMPLOYMENT_TYPE_LABELS[type]}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {formData.employmentType === EMPLOYMENT_TYPE.FULLTIME && (
                  <>
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth required>
                        <InputLabel>Candidate Level</InputLabel>
                        <Select
                          value={formData.candidateLevel}
                          label="Candidate Level"
                          onChange={(e) => handleInputChange('candidateLevel', e.target.value)}
                          readOnly={isCommissionApproved()}
                        >
                          {Object.values(LEVEL).map((level) => (
                            <MenuItem key={level} value={level}>
                              {LEVEL_LABELS[level]}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth required>
                        <InputLabel>English Skill</InputLabel>
                        <Select
                          value={formData.englishSkill}
                          label="English Skill"
                          onChange={(e) => handleInputChange('englishSkill', e.target.value)}
                          readOnly={isCommissionApproved()}
                        >
                          {Object.values(ENGLISH_SKILL).map((skill) => (
                            <MenuItem key={skill} value={skill}>
                              {ENGLISH_SKILL_LABELS[skill]}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  </>
                )}

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth required>
                    <InputLabel>Commission Phase</InputLabel>
                    <Select
                      value={formData.commissionPhase}
                      label="Commission Phase"
                      onChange={(e) => handleInputChange('commissionPhase', e.target.value)}
                      readOnly={isCommissionApproved()}
                    >
                      {Object.values(COMMISSION_PHASE).map((phase) => (
                        <MenuItem key={phase} value={phase}>
                          {COMMISSION_PHASE_LABELS[phase]}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    label="Amount"
                    type="text"
                    value={formatCurrency(formData.amount)}
                    fullWidth
                    required
                    InputProps={{
                      readOnly: true,
                      endAdornment: loadingAmount ? <CircularProgress size={20} /> : null,
                    }}
                    helperText="Amount is automatically calculated based on employment type, candidate level, English skill, and commission phase"
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth required>
                    <InputLabel>Status</InputLabel>
                    <Select
                      value={formData.status}
                      label="Status"
                      onChange={(e) => handleInputChange('status', e.target.value)}
                    >
                      {Object.values(COMMISSION_STATUS).map((status) => (
                        <MenuItem key={status} value={status}>
                          {COMMISSION_STATUS_LABELS[status]}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    label="Onboarding Date"
                    type="date"
                    value={formData.onboardingDate}
                    onChange={(e) => handleInputChange('onboardingDate', e.target.value)}
                    fullWidth
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    label="Official Date"
                    type="date"
                    value={formData.officialDate}
                    onChange={(e) => handleInputChange('officialDate', e.target.value)}
                    fullWidth
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    label="Paid Date"
                    type="date"
                    value={formData.paidDate}
                    onChange={(e) => handleInputChange('paidDate', e.target.value)}
                    fullWidth
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    label="Notes"
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    fullWidth
                    multiline
                    rows={4}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                    <Button
                      variant="outlined"
                      onClick={() => navigate(isCreateMode ? '/employer-commissions' : `/employer-commissions/${id}`)}
                      disabled={saving}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      startIcon={<Save />}
                      disabled={saving}
                    >
                      {saving ? (isCreateMode ? 'Creating...' : 'Saving...') : (isCreateMode ? 'Create Commission' : 'Save Changes')}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Paper>
        </Box>
      </div>
    </div>
  );
};

export default EmployerCommissionEditPage;
