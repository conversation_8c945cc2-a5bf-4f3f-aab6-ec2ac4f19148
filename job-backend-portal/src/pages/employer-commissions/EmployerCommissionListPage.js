import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
  Button,
  TextField,
  Pagination,
  CircularProgress,
  Alert,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Grid
} from '@mui/material';
import { Plus } from 'lucide-react';
import {
  getAllEmployerCommissions,
  deleteEmployerCommission,
  approveCommission
} from '../../services/employerCommissionService';
import { seedPaymentRates } from '../../services/paymentRateService';
import {
  COMMISSION_STATUS,
  COMMISSION_PHASE,
  COMMISSION_STATUS_LABELS,
  COMMISSION_PHASE_LABELS,
  EMPLOYMENT_TYPE_LABELS,
  LEVEL_LABELS,
  ENGLISH_SKILL_LABELS
} from '../../constants/collaboratorPolicy';
import { formatDateShort } from '../../utils/dateUtils';
import { formatCurrency } from '../../utils/currencyUtils';

const EmployerCommissionListPage = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [appliedSearchTerm, setAppliedSearchTerm] = useState('');
  const [commissions, setCommissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [rowsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [statusFilter, setStatusFilter] = useState('');
  const [phaseFilter, setPhaseFilter] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [commissionToDelete, setCommissionToDelete] = useState(null);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [seeding, setSeeding] = useState(false);

  const loadCommissions = async () => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: rowsPerPage,
        search: appliedSearchTerm,
        status: statusFilter || undefined,
        commissionPhase: phaseFilter || undefined
      };

      const response = await getAllEmployerCommissions(params);
      setCommissions(response.items);
      setTotalPages(response.meta.totalPages);
      setError(null);
    } catch (error) {
      console.error('Failed to load employer commissions:', error);
      setError('Failed to load employer commissions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCommissions();
  }, [page, rowsPerPage, appliedSearchTerm, statusFilter, phaseFilter]);

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  const handleSearch = () => {
    setAppliedSearchTerm(searchTerm);
    setPage(1);
  };

  const handleDeleteClick = (commission) => {
    setCommissionToDelete(commission);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (commissionToDelete) {
      try {
        await deleteEmployerCommission(commissionToDelete.id);
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
        loadCommissions();
      } catch (error) {
        setError('Failed to delete commission');
        console.error('Error deleting commission:', error);
      } 
    }
    setDeleteDialogOpen(false);
    setCommissionToDelete(null);
  };

  const handleSeedData = async () => {
    setSeeding(true);
    setError(null);
    try {
      await seedPaymentRates();
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (error) {
      setError('Failed to seed payment rates data');
      console.error('Error seeding data:', error);
    } finally {
      setSeeding(false);
    }
  };

  const handleApproveClick = async (commission) => {
    try {
      await approveCommission(commission.id);
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
      loadCommissions();
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to approve commission';
      setError(errorMessage);
      console.error('Error approving commission:', error);
    }
  };

  const getStatusChipColor = (status) => {
    switch (status) {
      case COMMISSION_STATUS.PENDING:
        return 'warning';
      case COMMISSION_STATUS.APPROVED:
        return 'info';
      case COMMISSION_STATUS.CANCELLED:
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', p: 3 }}>
            <CircularProgress />
          </Box>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h5" component="h1">
              Employer Commission Management
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleSeedData}
                disabled={seeding}
              >
                {seeding ? 'Seeding...' : 'Seed Payment Rates'}
              </Button>
              <Button
                variant="contained"
                color="primary"
                startIcon={<Plus />}
                component={Link}
                to="/employer-commissions/create"
              >
                Add New Commission
              </Button>
            </Box>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Operation completed successfully!
            </Alert>
          )}

          <Paper sx={{ p: 2, mb: 3 }}>
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} md={4}>
                <TextField
                  label="Search Commissions"
                  variant="outlined"
                  size="small"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleSearch();
                    }
                  }}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    label="Status"
                    onChange={(e) => setStatusFilter(e.target.value)}
                  >
                    <MenuItem value="">All Statuses</MenuItem>
                    {Object.values(COMMISSION_STATUS).map((status) => (
                      <MenuItem key={status} value={status}>
                        {COMMISSION_STATUS_LABELS[status]}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Phase</InputLabel>
                  <Select
                    value={phaseFilter}
                    label="Phase"
                    onChange={(e) => setPhaseFilter(e.target.value)}
                  >
                    <MenuItem value="">All Phases</MenuItem>
                    {Object.values(COMMISSION_PHASE).map((phase) => (
                      <MenuItem key={phase} value={phase}>
                        {COMMISSION_PHASE_LABELS[phase]}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <Button
                  onClick={handleSearch}
                  variant="contained"
                  size="small"
                  fullWidth
                >
                  Search
                </Button>
              </Grid>
            </Grid>

            <Box sx={{ width: '100%', overflowX: 'auto' }}>
              <Table sx={{ minWidth: 1400 }}>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Employer Email</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Applicant</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Employment Type</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Level</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>English Skill</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Phase</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Amount</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Onboarding Date</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Official Date</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Paid Date</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {commissions.map((commission) => (
                    <TableRow
                      key={commission.id}
                      onDoubleClick={() => navigate(`/employer-commissions/${commission.id}`)}
                      sx={{
                        cursor: 'pointer',
                        '&:hover': {
                          backgroundColor: 'rgba(0, 0, 0, 0.04)'
                        }
                      }}
                    >
                      <TableCell>{commission.employerEmail}</TableCell>
                      <TableCell>
                        {commission.applicantName && (
                          <Link
                            to={`/applicants/${commission.jobApplicationId}`}
                            style={{ textDecoration: 'none' }}
                          >
                            {commission.applicantName}
                          </Link>
                        )}
                      </TableCell>
                      <TableCell>{EMPLOYMENT_TYPE_LABELS[commission.employmentType]}</TableCell>
                      <TableCell>{LEVEL_LABELS[commission.candidateLevel]}</TableCell>
                      <TableCell>{ENGLISH_SKILL_LABELS[commission.englishSkill]}</TableCell>
                      <TableCell>{COMMISSION_PHASE_LABELS[commission.commissionPhase]}</TableCell>
                      <TableCell>
                        <Chip
                          label={COMMISSION_STATUS_LABELS[commission.status]} 
                          color={getStatusChipColor(commission.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{formatCurrency(commission.amount)}</TableCell>
                      <TableCell>
                        {formatDateShort(commission.onboardingDate)}
                      </TableCell>
                      <TableCell>
                        {formatDateShort(commission.officialDate)}
                      </TableCell>
                      <TableCell>
                        {formatDateShort(commission.paidDate)}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', justifyContent: 'end', gap: 1 }}>
                          {commission.status === COMMISSION_STATUS.PENDING && (
                            <Button
                              variant="outlined"
                              color="success"
                              size="small"
                              onClick={() => handleApproveClick(commission)}
                            >
                              Approve
                            </Button>
                          )}
                          <Button
                            variant="outlined"
                            size="small"
                            component={Link}
                            to={`/employer-commissions/${commission.id}/edit`}
                          >
                            Edit
                          </Button>
                          <Button
                            variant="outlined"
                            color="error"
                            size="small"
                            onClick={() => handleDeleteClick(commission)}
                          >
                            Delete
                          </Button>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handleChangePage}
                color="primary"
              />
            </Box>
          </Paper>
        </Box>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={deleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
        >
          <DialogTitle>Confirm Delete</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete this commission? This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleDeleteConfirm} color="error" autoFocus>
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    </div>
  );
};

export default EmployerCommissionListPage;
