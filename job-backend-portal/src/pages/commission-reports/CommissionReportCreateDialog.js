import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Box,
  Typography,
  Checkbox,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  Grid,
  Link
} from '@mui/material';
import {
  getEmployerEmails,
  createCommissionReport
} from '../../services/commissionReportService';
import { COMMISSION_STATUS, PAYMENT_STATUS } from '../../constants/collaboratorPolicy';
import {
  getAllEmployerCommissions
} from '../../services/employerCommissionService';
import { formatDate } from '../../utils/dateUtils';
import {
  COMMISSION_PHASE_LABELS,
  EMPLOYMENT_TYPE_LABELS,
  LEVEL_LABELS,
  ENGLISH_SKILL_LABELS
} from '../../constants/collaboratorPolicy';
import { formatCurrency } from '../../utils/currencyUtils';

const CommissionReportCreateDialog = ({ open, onClose, onSuccess }) => {
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Step 1 data
  const [employerEmails, setEmployerEmails] = useState([]);
  const [selectedEmployerEmail, setSelectedEmployerEmail] = useState('');
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  
  // Step 2 data
  const [availableCommissions, setAvailableCommissions] = useState([]);
  const [selectedCommissions, setSelectedCommissions] = useState([]);
  const [loadingCommissions, setLoadingCommissions] = useState(false);
  
  // Form data
  const [notes, setNotes] = useState('');

  const calculateTotalAmount = () => {
    return selectedCommissions.reduce((sum, commission) => sum + Number(commission.amount), 0);
  };

  useEffect(() => {
    if (open) {
      loadEmployerEmails();
      resetForm();
    }
  }, [open]);

  const resetForm = () => {
    setStep(1);
    setSelectedEmployerEmail('');
    setSelectedMonth(new Date().getMonth() + 1);
    setSelectedYear(new Date().getFullYear());
    setAvailableCommissions([]);
    setSelectedCommissions([]);
    setNotes('');
    setError(null);
  };

  const loadEmployerEmails = async () => {
    setLoading(true);
    try {
      const emails = await getEmployerEmails();
      setEmployerEmails(emails);
      setError(null);
    } catch (error) {
      console.error('Failed to load employer emails:', error);
      setError('Failed to load employer emails');
    } finally {
      setLoading(false);
    }
  };

  const handleNext = async () => {
    if (!selectedEmployerEmail || !selectedMonth || !selectedYear) {
      setError('Please select employer email, month and year');
      return;
    }

    setLoadingCommissions(true);
    try {
      const params = {
        page: -1,
        limit: -1,
        employerEmail: selectedEmployerEmail,
        status: COMMISSION_STATUS.APPROVED,
        report: null
      };
      const response = await getAllEmployerCommissions(params);
      const commissions = response.items;
      
      if (commissions.length === 0) {
        setError('No approved commissions that are still unpaid for the selected employer');
        return;
      }
      
      setAvailableCommissions(commissions);
      setSelectedCommissions(commissions);
      setStep(2);
      setError(null);
    } catch (error) {
      console.error('Failed to load commissions:', error);
      setError('Failed to load commissions for the selected criteria');
    } finally {
      setLoadingCommissions(false);
    }
  };

  const handleBack = () => {
    setStep(1);
    setError(null);
  };

  const handleCommissionToggle = (commission) => {
    setSelectedCommissions(prev => {
      const isSelected = prev.find(c => c.id === commission.id);
      if (isSelected) {
        return prev.filter(c => c.id !== commission.id);
      } else {
        return [...prev, commission];
      }
    });
  };

  const handleSelectAll = () => {
    if (selectedCommissions.length === availableCommissions.length) {
      setSelectedCommissions([]);
    } else {
      setSelectedCommissions([...availableCommissions]);
    }
  };

  const handleCreate = async () => {
    if (selectedCommissions.length === 0) {
      setError('Please select at least one commission');
      return;
    }

    setLoading(true);
    try {
      const reportData = {
        employerEmail: selectedEmployerEmail,
        month: selectedMonth,
        year: selectedYear,
        totalAmount: calculateTotalAmount(),
        commissionIds: selectedCommissions.map(c => c.id),
        notes: notes || undefined
      };

      await createCommissionReport(reportData);
      onSuccess();
    } catch (error) {
      console.error('Failed to create commission report:', error);
      setError('Failed to create commission report');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
    >
        <DialogTitle>
          Create Commission Report - {step === 1 ? 'Select Infomation' : 'Select Commissions'}
        </DialogTitle>
        
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {step === 1 && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Employer Email</InputLabel>
                    <Select
                      value={selectedEmployerEmail}
                      onChange={(e) => setSelectedEmployerEmail(e.target.value)}
                      label="Employer Email"
                      disabled={loading}
                    >
                      {employerEmails.map((email) => (
                        <MenuItem key={email} value={email}>
                          {email}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Month</InputLabel>
                    <Select
                      value={selectedMonth}
                      onChange={(e) => setSelectedMonth(e.target.value)}
                      label="Month"
                      disabled={loading}
                    >
                      {Array.from({ length: 12 }, (_, i) => (
                        <MenuItem key={i + 1} value={i + 1}>
                          {i + 1}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Year</InputLabel>
                    <Select
                      value={selectedYear}
                      onChange={(e) => setSelectedYear(e.target.value)}
                      label="Year"
                      disabled={loading}
                    >
                      {Array.from({ length: 5 }, (_, i) => {
                        const year = new Date().getFullYear() - i;
                        return (
                          <MenuItem key={year} value={year}>
                            {year}
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>

              {loading && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                  <CircularProgress />
                </Box>
              )}
            </Box>
          )}

          {step === 2 && (
            <Box sx={{ mt: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Commissions for {selectedEmployerEmail} - {selectedMonth.toString().padStart(2, '0')}/{selectedYear}
                </Typography>
                <Box>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={selectedCommissions.length === availableCommissions.length && availableCommissions.length > 0}
                        indeterminate={selectedCommissions.length > 0 && selectedCommissions.length < availableCommissions.length}
                        onChange={handleSelectAll}
                      />
                    }
                    label="Select All"
                  />
                  <Typography variant="body2" color="textSecondary" sx={{ ml: 2 }}>
                    Selected: {selectedCommissions.length} / {availableCommissions.length}
                  </Typography>
                </Box>
              </Box>

              {loadingCommissions ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <Paper sx={{ maxHeight: 400, overflow: 'auto' }}>
                  <Table stickyHeader>
                    <TableHead>
                      <TableRow>
                        <TableCell padding="checkbox">Select</TableCell>
                        <TableCell>Applicant</TableCell>
                        <TableCell>Employment Type</TableCell>
                        <TableCell>Level</TableCell>
                        <TableCell>English Skill</TableCell>
                        <TableCell>Phase</TableCell>
                        <TableCell>Amount</TableCell>
                        <TableCell>Created Date</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {availableCommissions.map((commission) => (
                        <TableRow key={commission.id}>
                          <TableCell padding="checkbox">
                            <Checkbox
                              checked={selectedCommissions.some(c => c.id === commission.id)}
                              onChange={() => handleCommissionToggle(commission)}
                            />
                          </TableCell>
                          <TableCell>
                            {commission.applicantName && (
                              <Link
                                to={`/applicants/${commission.jobApplicationId}`}
                                style={{ textDecoration: 'none' }}
                              >
                                {commission.applicantName}
                              </Link>
                            )}
                          </TableCell>
                          <TableCell>{EMPLOYMENT_TYPE_LABELS[commission.employmentType]}</TableCell>
                          <TableCell>{commission.candidateLevel ? LEVEL_LABELS[commission.candidateLevel] : '-'}</TableCell>
                          <TableCell>{commission.englishSkill ? ENGLISH_SKILL_LABELS[commission.englishSkill] : '-'}</TableCell>
                          <TableCell>{COMMISSION_PHASE_LABELS[commission.commissionPhase]}</TableCell>
                          <TableCell>{formatCurrency(commission.amount)}</TableCell>
                          <TableCell>{formatDate(commission.createdAt)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </Paper>
              )}

              {selectedCommissions.length > 0 && (
                <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', border: 1, borderColor: 'divider', borderRadius: 1 }}>
                  <Typography variant="h6" gutterBottom>
                    Summary
                  </Typography>
                  <Typography variant="body1">
                    Selected Commissions: {selectedCommissions.length}
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    Total Amount: {formatCurrency(calculateTotalAmount())}
                  </Typography>
                </Box>
              )}

              <TextField
                fullWidth
                label="Notes (Optional)"
                multiline
                rows={3}
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                sx={{ mt: 2 }}
              />
            </Box>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          
          {step === 1 ? (
            <Button
              onClick={handleNext}
              variant="contained"
              disabled={loading || !selectedEmployerEmail || !selectedMonth || !selectedYear}
            >
              {loadingCommissions ? 'Loading...' : 'Next'}
            </Button>
          ) : (
            <>
              <Button onClick={handleBack} disabled={loading}>
                Back
              </Button>
              <Button 
                onClick={handleCreate} 
                variant="contained"
                disabled={loading || selectedCommissions.length === 0}
              >
                {loading ? 'Creating...' : 'Create report'}
              </Button>
            </>
          )}
        </DialogActions>
      </Dialog>
    );
};

export default CommissionReportCreateDialog;
