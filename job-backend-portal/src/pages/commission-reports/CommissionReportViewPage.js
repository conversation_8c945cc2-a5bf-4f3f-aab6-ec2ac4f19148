import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  Chip,
  CircularProgress,
  Alert,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow
} from '@mui/material';
import { ArrowLeft, Edit, CheckCircle, DollarSign } from 'lucide-react';
import {
  getCommissionReportById,
  approveCommissionReport,
  markCommissionReportAsPaid
} from '../../services/commissionReportService';
import {
  REPORT_STATUS,
  REPORT_STATUS_LABELS,
  COMMISSION_PHASE_LABELS,
  EMPLOYMENT_TYPE_LABELS,
  LEVEL_LABELS,
  ENGLISH_SKILL_LABELS,
  PAYMENT_STATUS,
  PAYMENT_STATUS_LABELS
} from '../../constants/collaboratorPolicy';
import { formatDate } from '../../utils/dateUtils';
import { formatCurrency } from '../../utils/currencyUtils';
import { useToast } from "../../contexts/ToastContext";

const CommissionReportViewPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { showToast } = useToast();
  
  const [report, setReport] = useState(null);
  const [commissions, setCommissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);

  const getStatusColor = (status) => {
    switch (status) {
      case REPORT_STATUS.PENDING:
        return 'warning';
      case REPORT_STATUS.APPROVED:
        return 'info';
      case REPORT_STATUS.PAID:
        return 'success';
      case REPORT_STATUS.CANCELLED:
        return 'error';
      default:
        return 'default';
    }
  };

  const loadReportData = async () => {
    setLoading(true);
    try {
      const reportData = await getCommissionReportById(id);
      setReport(reportData);

      // Load related commissions
      if (reportData.commissions) {
        setCommissions(reportData.commissions);
      }

      setError(null);
    } catch (error) {
      console.error('Failed to load commission report:', error);
      setError('Failed to load commission report');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      loadReportData();
    }
  }, [id]);

  const handleApprove = async () => {
    setActionLoading(true);
    try {
      await approveCommissionReport(id);
      showToast('Commission report approved successfully', 'success');
      loadReportData(); // Reload data
    } catch (error) {
      console.error('Failed to approve commission report:', error);
      showToast('Failed to approve commission report', 'error');
    } finally {
      setActionLoading(false);
    }
  };

  const handleMarkAsPaid = async () => {
    setActionLoading(true);
    try {
      await markCommissionReportAsPaid(id);
      showToast('Commission report marked as paid successfully', 'success');
      loadReportData(); // Reload data
    } catch (error) {
      console.error('Failed to mark commission report as paid:', error);
      showToast('Failed to mark commission report as paid', 'error');
    } finally {
      setActionLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress />
          </Box>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{ p: 3 }}>
            <Alert severity="error">{error}</Alert>
          </Box>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" component="h1">
              Commission Report Details
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<ArrowLeft />}
                component={Link}
                to="/commission-reports"
              >
                Back to List
              </Button>
              {report?.status === REPORT_STATUS.PENDING && (
                <Button
                  variant="contained"
                  color="success"
                  startIcon={<CheckCircle />}
                  onClick={handleApprove}
                  disabled={actionLoading}
                >
                  {actionLoading ? 'Approving...' : 'Approve'}
                </Button>
              )}
              {report?.status === REPORT_STATUS.APPROVED && report.paymentStatus !== PAYMENT_STATUS.PAID && (
                <Button
                  variant="contained"
                  color="info"
                  startIcon={<DollarSign />}
                  onClick={handleMarkAsPaid}
                  disabled={actionLoading}
                >
                  {actionLoading ? 'Processing...' : 'Mark as Paid'}
                </Button>
              )}
              <Button
                variant="contained"
                startIcon={<Edit />}
                component={Link}
                to={`/commission-reports/${id}/edit`}
              >
                Edit
              </Button>
            </Box>
          </Box>

          {report && (
            <>
              {/* Report Information */}
              <Paper sx={{ p: 3, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Report Information
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" color="textSecondary">
                      Employer Email
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {report.employerEmail}
                    </Typography>

                    <Typography variant="body2" color="textSecondary">
                      Month/Year
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {report.month}/{report.year}
                    </Typography>

                    <Typography variant="body2" color="textSecondary">
                      Total Amount
                    </Typography>
                    <Typography variant="h6" color="primary" gutterBottom>
                      {formatCurrency(report.totalAmount)}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" color="textSecondary">
                      Status
                    </Typography>
                    <Box sx={{ mb: 2 }}>
                      <Chip
                        label={REPORT_STATUS_LABELS[report.status]}
                        color={getStatusColor(report.status)}
                        size="medium"
                      />
                    </Box>

                    <Typography variant="body2" color="textSecondary">
                      Created Date
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {formatDate(report.createdAt)}
                    </Typography>

                    {report.approvedBy && (
                      <>
                        <Typography variant="body2" color="textSecondary">
                          Approved By
                        </Typography>
                        <Typography variant="body1" gutterBottom>
                          {report.approvedBy}
                        </Typography>

                        <Typography variant="body2" color="textSecondary">
                          Approved Date
                        </Typography>
                        <Typography variant="body1" gutterBottom>
                          {formatDate(report.approvedAt)}
                        </Typography>
                      </>
                    )}

                    <Typography variant="body2" color="textSecondary">
                      Payment Status
                    </Typography>
                    <Box sx={{ mb: 2 }}>
                      <Chip
                        label={PAYMENT_STATUS_LABELS[report.paymentStatus]}
                        color={report.paymentStatus === 'Paid' ? 'success' : 'warning'}
                        size="medium"
                      />
                    </Box>

                    {report.paidDate && (
                      <>
                        <Typography variant="body2" color="textSecondary">
                          Paid Date
                        </Typography>
                        <Typography variant="body1" gutterBottom>
                          {formatDate(report.paidDate)}
                        </Typography>
                      </>
                    )}
                  </Grid>

                  {report.notes && (
                    <Grid item xs={12}>
                      <Typography variant="body2" color="textSecondary">
                        Notes
                      </Typography>
                      <Typography variant="body1">
                        {report.notes}
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              </Paper>

              {/* Related Commissions */}
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Related Commissions ({commissions.length})
                </Typography>
                <Divider sx={{ mb: 2 }} />
                
                {commissions.length > 0 ? (
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Applicant</TableCell>
                        <TableCell>Employment Type</TableCell>
                        <TableCell>Level</TableCell>
                        <TableCell>English Skill</TableCell>
                        <TableCell>Phase</TableCell>
                        <TableCell>Amount</TableCell>
                        <TableCell>Created Date</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {commissions.map((commission) => (
                        <TableRow key={commission.id} onDoubleClick={() => navigate(`/employer-commissions/${commission.id}`)}>
                          <TableCell>
                            {commission.applicantName && (
                              <Link
                                to={`/applicants/${commission.jobApplicationId}`}
                                style={{ textDecoration: 'none' }}
                              >
                                {commission.applicantName}
                              </Link>
                            )}
                          </TableCell>
                          <TableCell>{EMPLOYMENT_TYPE_LABELS[commission.employmentType]}</TableCell>
                          <TableCell>{commission.candidateLevel ? LEVEL_LABELS[commission.candidateLevel] : '-'}</TableCell>
                          <TableCell>{commission.englishSkill ? ENGLISH_SKILL_LABELS[commission.englishSkill] : '-'}</TableCell>
                          <TableCell>{COMMISSION_PHASE_LABELS[commission.commissionPhase]}</TableCell>
                          <TableCell>{formatCurrency(commission.amount)}</TableCell>
                          <TableCell>{formatDate(commission.createdAt)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <Typography variant="body2" color="textSecondary">
                    No commissions found for this report.
                  </Typography>
                )}
              </Paper>
            </>
          )}
        </Box>
      </div>
    </div>
  );
};

export default CommissionReportViewPage;
