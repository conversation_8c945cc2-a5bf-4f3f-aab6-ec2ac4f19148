import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  LogOut,
  ChevronLeft,
  ChevronRight,
  LayoutDashboard,
  Briefcase,
  Settings2,
  Wrench,
  FileText,
  Users,
  MessageSquare,
  Search,
  Building2,
  UserCircle,
  Tags,
  LayoutList,
  BookOpen,
  BadgeDollarSign
} from 'lucide-react';
import { useUser } from '../contexts/UserContext';
import './Sidebar.css';

function Sidebar({ onCollapse }) {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout: userLogout } = useUser();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
    if (onCollapse) {
      onCollapse(!isCollapsed);
    }
  };

  const isActive = (path) => {
    return location.pathname.startsWith(path);
  };

  const handleLogout = () => {
    userLogout();
    navigate('/login');
  };

  // Define menu items for each role
  const adminMenuItems = [
    {
      id: 'dashboard',
      icon: <LayoutDashboard size={20} />,
      label: 'Dashboard',
      path: '/admin-dashboard'
    },
    {
      id: 'content',
      icon: <LayoutList size={20} />,
      label: 'Content',
      submenu: [
        {
          id: 'blogs',
          label: 'Blogs',
          path: '/blogs',
          icon: <BookOpen size={16} />
        },
        {
          id: 'categories',
          icon: <Tags size={16} />,
          label: 'Categories',
          path: '/categories'
        },
        {
          id: 'contacts',
          icon: <MessageSquare size={16} />,
          label: 'Contacts',
          path: '/contacts'
        }
      ]
    },
    {
      id: 'jobs',
      icon: <Briefcase size={20} />,
      label: 'Jobs',
      submenu: [
        {
          id: 'jobs-info',
          label: 'Jobs Information',
          path: '/jobs',
          icon: <FileText size={16} />
        },
        {
          id: 'applicants',
          label: 'Applicants',
          path: '/applicants',
          icon: <Briefcase size={16} />
        },
        {
          id: 'employer-commissions',
          icon: <BadgeDollarSign size={16} />,
          label: 'Employer Commissions',
          path: '/employer-commissions'
        },
        {
          id: 'commission-reports',
          icon: <FileText size={16} />,
          label: 'Commission Reports',
          path: '/commission-reports'
        }
      ]
    },
    {
      id: 'system',
      icon: <Settings2 size={20} />,
      label: 'System Settings',
      submenu: [
        {
          id: 'users',
          icon: <Users size={16} />,
          label: 'Users',
          path: '/users'
        },
        {
          id: 'options',
          icon: <Tags size={16} />,
          label: 'Options',
          path: '/options'
        }
      ]
    },
    {
      id: 'collaborator-policy',
      icon: <Building2 size={20} />,
      label: 'Collaborator policy',
      path: '/employer/collaborator-policy'
    }
  ];

  const employerMenuItems = [
    {
      id: 'dashboard',
      icon: <LayoutDashboard size={20} />,
      label: 'Dashboard',
      path: '/employer-dashboard'
    },
    {
      id: 'jobs',
      icon: <Briefcase size={20} />,
      label: 'My Jobs',
      submenu: [
        {
          id: 'manage-jobs',
          label: 'Manage Jobs',
          path: '/employer/jobs',
          icon: <Wrench size={16} />
        },
        {
          id: 'job-applications',
          label: 'Job Applications',
          path: '/employer/applicants',
          icon: <Users size={16} />
        }
      ]
    },
    {
      id: 'collaborator-policy',
      icon: <Building2 size={20} />,
      label: 'Collaborator policy',
      path: '/employer/collaborator-policy'
    }
  ];

  const userMenuItems = [
    {
      id: 'dashboard',
      icon: <LayoutDashboard size={20} />,
      label: 'Dashboard',
      path: '/user-dashboard'
    },
    {
      id: 'jobs',
      icon: <Search size={20} />,
      label: 'Find Jobs',
      path: '/jobs'
    },
    {
      id: 'applicants',
      icon: <Briefcase size={20} />,
      label: 'My Applications',
      path: '/my-applicants'
    },
    {
      id: 'profile',
      icon: <UserCircle size={20} />,
      label: 'My Profile',
      path: '/profile'
    }
  ];

  const hrMenuItems = [
    {
      id: 'dashboard',
      icon: <LayoutDashboard size={20} />,
      label: 'Dashboard',
      path: '/hr-dashboard'
    },
    {
      id: 'jobs',
      icon: <Briefcase size={20} />,
      label: 'Jobs',
      path: '/jobs'
    },
    {
      id: 'applicants',
      icon: <Briefcase size={20} />,
      label: 'Applicants',
      path: '/applicants'
    },
    {
      id: 'profile',
      icon: <UserCircle size={20} />,
      label: 'My Profile',
      path: '/profile'
    }
  ];

  // Select menu items based on user role
  const getMenuItems = () => {
    switch (user.role?.toLowerCase()) {
      case 'hr':
        return hrMenuItems;
      case 'employer':
        return employerMenuItems;
      case 'user':
        return userMenuItems;
      case 'admin':
      default:
        return adminMenuItems;
    }
  };

  const menuItems = getMenuItems();

  return (
    <div className={`sidebar ${isCollapsed ? 'collapsed' : ''}`}>
      <div className="sidebar-header">
        <button className="collapse-btn" onClick={toggleCollapse}>
          {isCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
        </button>
      </div>

      <nav className="sidebar-nav">
        {menuItems.map((item) => (
          <div key={item.id} className="nav-item">
            {item.submenu ? (
              <div className="nav-item-with-submenu">
                <div className={`nav-link ${isActive(item.path) ? 'active' : ''}`}>
                  {item.icon}
                  {!isCollapsed && <span>{item.label}</span>}
                </div>
                {!isCollapsed && (
                  <div className="submenu">
                    {item.submenu.map((subItem) => (
                      <Link
                        key={subItem.id}
                        to={subItem.path}
                        className={`nav-link ${isActive(subItem.path) ? 'active' : ''}`}
                      >
                        {subItem.icon}
                        <span>{subItem.label}</span>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <Link
                to={item.path}
                className={`nav-link ${isActive(item.path) ? 'active' : ''}`}
              >
                {item.icon}
                {!isCollapsed && <span>{item.label}</span>}
              </Link>
            )}
          </div>
        ))}
      </nav>

      <div className="sidebar-footer">
        <button className="logout-btn" onClick={handleLogout}>
          <LogOut size={20} />
          {!isCollapsed && <span>Logout</span>}
        </button>
      </div>
    </div>
  );
}

export default Sidebar;